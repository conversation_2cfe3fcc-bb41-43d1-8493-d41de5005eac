import puppeteer from 'puppeteer'
import path from 'path'
import fs from 'fs'
import _ from 'lodash'
import Renderer from '../../../lib/renderer/loader.js'
import { _path, PLUGIN_NAME } from './constants.js'

const renderer = Renderer.getRenderer()

/**
 * 狼人杀插件的 Puppeteer 管理器
 * 负责浏览器实例管理和渲染功能
 */
class Puppeteer {
  constructor(logger) {
    this.browser = null
    this.lock = false
    this.shoting = []
    this.logger = logger
    /** 截图数达到时重启浏览器 避免生成速度越来越慢 */
    this.restartNum = 100
    /** 截图次数 */
    this.renderNum = 0
    this.config = {
      executablePath: '',
      puppeteerWS: '',
      headless: 'new',
      args: [
        '--disable-gpu',
        '--disable-setuid-sandbox',
        '--no-sandbox',
        '--no-zygote',
        '--font-render-hinting=medium',
        '--disable-application-cache',
        '--disable-dev-shm-usage', // 禁用/dev/shm使用
        '--disable-extensions', // 禁用扩展
        '--disable-infobars', // 禁用信息栏
        '--disable-notifications', // 禁用通知
        '--disable-offline-load-stale-cache', // 禁用离线加载过期缓存
        '--dns-prefetch-disable', // 禁用DNS预取
        '--enable-features=NetworkService', // 启用网络服务特性
        '--enable-automation' // 启用自动化
      ]
    }
  }

  /**
   * 初始化浏览器实例
   * @returns {Promise<boolean|object>} 浏览器实例或失败状态
   */
  async browserInit() {
    if (this.browser) return this.browser
    if (this.lock) return false
    this.lock = true

    this.logger.mark('[狼人杀插件] Puppeteer 启动中...')
    const browserURL = 'http://127.0.0.1:51777'
    try {
      // 尝试连接已存在的浏览器实例
      this.browser = await puppeteer.connect({ browserURL })
    } catch (e) {
      // 连接失败，启动新的浏览器实例
      this.browser = await puppeteer.launch(this.config).catch((err) => {
        this.logger.error(err.toString())
        if (String(err).includes('correct Chromium')) {
          this.logger.error('没有正确安装Chromium，可以尝试执行安装命令：node ./node_modules/puppeteer/install.js')
        }
      })
    }
    this.lock = false

    if (!this.browser) {
      this.logger.error('[狼人杀插件] Puppeteer 启动失败')
      return false
    }

    this.logger.mark('[狼人杀插件] Puppeteer 启动成功')

    /** 监听Chromium实例是否断开 */
    this.browser.on('disconnected', () => {
      this.logger.info('[狼人杀插件] Chromium实例关闭或崩溃！')
      this.browser = null
    })
    return this.browser
  }

  /**
   * 创建新页面
   * @returns {Promise<Page>} 页面实例
   */
  async newPage() {
    if (!(await this.browserInit())) {
      return false
    }
    return await this.browser.newPage().catch((err) => {
      this.logger.error('[狼人杀插件] 创建页面失败：' + err)
      return false
    })
  }

  /**
   * 关闭页面
   * @param {Page} page 页面实例
   */
  async closePage(page) {
    if (page) {
      await page.close().catch((err) => this.logger.error('[狼人杀插件] 页面关闭出错：' + err))
      this.renderNum += 1
      this.restart()
    }
  }

  /**
   * 关闭浏览器
   */
  async close() {
    if (this.browser) {
      await this.browser.close().catch((err) => this.logger.error('[狼人杀插件] 浏览器关闭出错：' + err))
      this.browser = null
    }
  }

  /**
   * 重启浏览器（当截图次数达到阈值时）
   */
  restart() {
    /** 截图超过重启数时，自动关闭重启浏览器，避免生成速度越来越慢 */
    if (this.renderNum % this.restartNum === 0) {
      if (this.shoting.length <= 0) {
        setTimeout(async () => {
          await this.close()
          this.logger.mark('[狼人杀插件] Puppeteer 关闭重启...')
        }, 100)
      }
    }
  }

  /**
   * 渲染HTML模板并发送消息
   * @param {string} tplPath 模板路径，相对于plugin resources目录
   * @param {Object} data 渲染数据
   * @param {Object} cfg 渲染配置，包含 e (通信句柄)
   * @returns {Promise<boolean>} 发送成功返回true，失败返回false
   */
  async renderAndSend(tplPath, data = {}, cfg = {}) {
    const { e } = cfg;
    if (!e) {
      this.logger.error('[狼人杀插件] renderAndSend 缺少通信句柄 e');
      return false;
    }

    try {
      const base64 = await this.render(tplPath, data, cfg);
      if (base64) {
        await e.reply(segment.image(`base64://${base64}`));
        return true;
      } else {
        await e.reply('图片生成失败，请稍后重试');
        return false;
      }
    } catch (error) {
      this.logger.error(`[狼人杀插件] renderAndSend 发送失败: ${error}`);
      if (e) {
        await e.reply('图片发送失败，请稍后重试');
      }
      return false;
    }
  }

  /**
   * 渲染HTML模板
   * @param {string} tplPath 模板路径，相对于plugin resources目录
   * @param {Object} data 渲染数据
   * @param {Object} cfg 渲染配置
   * @returns {Promise<string|boolean>} base64 截图或false
   */
  async render(tplPath, data = {}, cfg = {}) {
    this.shoting.push(true);
    try {
      // 处理传入的path
      tplPath = tplPath.replace(/.html$/, "")
      let paths = _.filter(tplPath.split("/"), (p) => !!p)
      tplPath = paths.join("/")

      // 创建临时目录
      const tempDir = path.join(_path, 'temp', 'html', PLUGIN_NAME, tplPath)
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true })
      }

      // 计算资源路径
      let pluResPath = `../../../${_.repeat("../", paths.length)}plugins/${PLUGIN_NAME}/resources/`

      // 渲染数据
      data = {
        sys: {
          scale: 1.2
        },
        _plugin: PLUGIN_NAME,
        _htmlPath: tplPath,
        pluResPath,
        tplFile: `./plugins/${PLUGIN_NAME}/resources/${tplPath}.html`,
        saveId: data.saveId || data.save_id || paths[paths.length - 1],

        // 截图参数
        imgType: 'jpeg',
        quality: 90,  // 图片质量
        omitBackground: false,
        pageGotoParams: {
          waitUntil: "networkidle0"
        },

        ...data
      }

      // 处理beforeRender回调
      if (cfg.beforeRender) {
        data = cfg.beforeRender({ data }) || data
      }

      // 调用渲染器进行截图
      let base64 = await renderer.screenshot(`${PLUGIN_NAME}/${tplPath}`, data)
      if (base64) {
        return base64
      }
      return false
    } finally {
      this.shoting.pop();
    }
  }
}

export default Puppeteer
